# Server Configuration
PORT=5000
NODE_ENV=production

# Database Configuration
MONGO_URI=mongodb+srv://username:<EMAIL>/database?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here-minimum-64-characters-long

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=
CLOUDINARY_API_KEY=
CLOUDINARY_API_SECRET=

# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Session Configuration
SESSION_SECRET=

# Frontend URL (for production)
FRONTEND_URL=

# Telegram Bot Configuration (Optional)
TELEGRAM_BOT_TOKEN=
TELEGRAM_BOT_USERNAME=

# Discord Bot Configuration (Optional)
DISCORD_BOT_TOKEN=
DISCORD_CLIENT_ID=

# Federation Configuration (Optional)
FEDERATION_REGISTRY_URL=
PLATFORM_URL=
PLATFORM_NAME=
FEDERATION_ENABLED=
ENABLE_CROSS_PLATFORM=
TELEGRAM_PLATFORM_URL=
DISCORD_PLATFORM_URL=

# Logging Configuration
LOG_LEVEL=error
ENABLE_SOCKET_LOGS=false
